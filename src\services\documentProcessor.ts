import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { OpenAIEmbeddings } from '@langchain/openai';
import { FaissStore } from '@langchain/community/vectorstores/faiss';
import { Document } from 'langchain/document';
import * as pdfParse from 'pdf-parse';
import * as mammoth from 'mammoth';

export interface ProcessedDocument {
  id: string;
  name: string;
  content: string;
  chunks: Document[];
  vectorStore?: FaissStore;
}

export interface DocumentProcessorConfig {
  openaiApiKey: string;
  chunkSize?: number;
  chunkOverlap?: number;
}

export class DocumentProcessor {
  private embeddings: OpenAIEmbeddings;
  private textSplitter: RecursiveCharacterTextSplitter;

  constructor(config: DocumentProcessorConfig) {
    this.embeddings = new OpenAIEmbeddings({
      openAIApiKey: config.openaiApiKey,
      modelName: 'text-embedding-3-small', // Cost-effective embedding model
    });

    this.textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: config.chunkSize || 1000,
      chunkOverlap: config.chunkOverlap || 200,
    });
  }

  async processFile(file: File): Promise<ProcessedDocument> {
    const content = await this.extractTextFromFile(file);
    const chunks = await this.splitText(content, file.name);
    
    return {
      id: this.generateId(),
      name: file.name,
      content,
      chunks,
    };
  }

  async processFiles(files: File[]): Promise<ProcessedDocument[]> {
    const processedDocs = await Promise.all(
      files.map(file => this.processFile(file))
    );
    return processedDocs;
  }

  async createVectorStore(documents: ProcessedDocument[]): Promise<FaissStore> {
    // Combine all chunks from all documents
    const allChunks = documents.flatMap(doc => doc.chunks);
    
    if (allChunks.length === 0) {
      throw new Error('No documents to process');
    }

    // Create vector store from documents
    const vectorStore = await FaissStore.fromDocuments(allChunks, this.embeddings);
    return vectorStore;
  }

  private async extractTextFromFile(file: File): Promise<string> {
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    switch (file.type) {
      case 'application/pdf':
        return await this.extractFromPDF(buffer);
      case 'text/plain':
        return buffer.toString('utf-8');
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return await this.extractFromWord(buffer);
      default:
        throw new Error(`Unsupported file type: ${file.type}`);
    }
  }

  private async extractFromPDF(buffer: Buffer): Promise<string> {
    try {
      const data = await pdfParse(buffer);
      return data.text;
    } catch (error) {
      console.error('Error extracting text from PDF:', error);
      throw new Error('Failed to extract text from PDF');
    }
  }

  private async extractFromWord(buffer: Buffer): Promise<string> {
    try {
      const result = await mammoth.extractRawText({ buffer });
      return result.value;
    } catch (error) {
      console.error('Error extracting text from Word document:', error);
      throw new Error('Failed to extract text from Word document');
    }
  }

  private async splitText(text: string, fileName: string): Promise<Document[]> {
    const docs = await this.textSplitter.createDocuments(
      [text],
      [{ source: fileName }]
    );
    return docs;
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}

export default DocumentProcessor;
