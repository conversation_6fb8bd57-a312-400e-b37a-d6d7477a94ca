export interface AppConfig {
  openaiApiKey: string;
}

export class ConfigService {
  private static readonly STORAGE_KEY = 'docuchat-config';

  static getConfig(): AppConfig | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
      
      // Fallback to environment variable
      const envKey = import.meta.env.VITE_OPENAI_API_KEY;
      if (envKey && envKey !== 'your_openai_api_key_here') {
        return { openaiApiKey: envKey };
      }
      
      return null;
    } catch (error) {
      console.error('Error loading config:', error);
      return null;
    }
  }

  static saveConfig(config: AppConfig): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(config));
    } catch (error) {
      console.error('Error saving config:', error);
      throw new Error('Failed to save configuration');
    }
  }

  static clearConfig(): void {
    localStorage.removeItem(this.STORAGE_KEY);
  }

  static isConfigured(): boolean {
    const config = this.getConfig();
    return config !== null && config.openaiApiKey.trim() !== '';
  }

  static getOpenAIApiKey(): string | null {
    const config = this.getConfig();
    return config?.openaiApiKey || null;
  }
}

export default ConfigService;
