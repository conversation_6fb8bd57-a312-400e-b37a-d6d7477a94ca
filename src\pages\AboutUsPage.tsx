import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Bot, 
  FileText, 
  Zap, 
  Search, 
  Shield, 
  Globe, 
  Rocket,
  ArrowLeft,
  Users,
  Target,
  Heart
} from 'lucide-react';
import { motion } from 'framer-motion';
import Navbar from '../components/layout/Navbar';

const AboutUsPage: React.FC = () => {
  const aboutSections = [
    {
      icon: Bot,
      title: "🤖 What We Do",
      description: "Docu Chatbot is an AI-powered assistant that helps users interact with and understand their documents by simply chatting with them — no technical skills required!",
      color: "text-blue-400"
    },
    {
      icon: FileText,
      title: "📄 Why We Built It",
      description: "Reading large documents can be overwhelming. We built Docu Chatbot to simplify the process by making document understanding fast, intelligent, and interactive.",
      color: "text-green-400"
    },
    {
      icon: Zap,
      title: "⚡ Our Mission",
      description: "To make document reading and information extraction effortless for everyone — whether you're a student, professional, researcher, or business owner.",
      color: "text-yellow-400"
    },
    {
      icon: Search,
      title: "🔍 What Makes Us Different",
      description: "Our chatbot uses powerful language models and smart search to answer your questions based on your documents — all in real-time.",
      color: "text-purple-400"
    },
    {
      icon: Shield,
      title: "🔐 Your Privacy Matters",
      description: "We prioritize user privacy. Your documents are never stored permanently, and all sessions are secure and confidential.",
      color: "text-red-400"
    },
    {
      icon: Globe,
      title: "🌍 Who We Serve",
      description: "Docu Chatbot is for anyone who works with contracts, reports, research papers, business files, legal docs, or educational material.",
      color: "text-cyan-400"
    },
    {
      icon: Rocket,
      title: "🚀 Our Vision",
      description: "To become the go-to intelligent assistant for document understanding across all industries and use cases.",
      color: "text-pink-400"
    }
  ];

  return (
    <div className="min-h-screen bg-navy-900">
      <Navbar />
      
      <div className="relative overflow-hidden">
        {/* Hero Section */}
        <div className="relative z-10 pt-20 pb-16 px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <div className="flex items-center justify-center mb-6">
                <Bot className="h-16 w-16 text-pink-400 mr-4" />
                <h1 className="text-5xl md:text-6xl font-bold text-white">
                  📘 About Us
                </h1>
              </div>
              <h2 className="text-2xl md:text-3xl font-bold text-pink-400 mb-4">
                Docu Chatbot
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Revolutionizing how people interact with documents through the power of AI and intelligent conversation.
              </p>
            </motion.div>

            {/* Back Button */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-8"
            >
              <Link
                to="/"
                className="inline-flex items-center text-pink-400 hover:text-pink-300 transition-colors duration-200"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Home
              </Link>
            </motion.div>
          </div>
        </div>

        {/* About Sections */}
        <div className="relative z-10 py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {aboutSections.map((section, index) => {
                const IconComponent = section.icon;
                return (
                  <motion.div
                    key={index}
                    className="bg-white/10 backdrop-blur-sm rounded-xl p-8 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-300 group"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                    whileHover={{ y: -5 }}
                  >
                    <div className="flex items-start mb-4">
                      <IconComponent className={`h-8 w-8 ${section.color} mr-4 mt-1 group-hover:scale-110 transition-transform duration-300`} />
                      <div>
                        <h3 className="text-xl font-bold text-white group-hover:text-pink-300 transition-colors duration-300 mb-3">
                          {section.title}
                        </h3>
                        <p className="text-gray-300 leading-relaxed">
                          {section.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Company Values Section */}
        <div className="relative z-10 py-16 px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
              className="bg-gradient-to-r from-pink-500/20 to-purple-600/20 backdrop-blur-sm rounded-2xl p-8 border border-pink-500/30"
            >
              <div className="text-center">
                <Heart className="h-12 w-12 text-pink-400 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-white mb-4">
                  Our Core Values
                </h3>
                <p className="text-gray-300 mb-8 leading-relaxed">
                  We believe in making technology accessible, secure, and genuinely helpful. Our platform is designed 
                  to empower users, not overwhelm them, while maintaining the highest standards of privacy and security.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-white/10 rounded-lg p-6">
                    <Users className="h-8 w-8 text-blue-400 mx-auto mb-3" />
                    <h4 className="text-white font-semibold mb-2">User-Centric</h4>
                    <p className="text-gray-400 text-sm">
                      Every feature is designed with the user experience in mind
                    </p>
                  </div>
                  <div className="bg-white/10 rounded-lg p-6">
                    <Shield className="h-8 w-8 text-green-400 mx-auto mb-3" />
                    <h4 className="text-white font-semibold mb-2">Privacy First</h4>
                    <p className="text-gray-400 text-sm">
                      Your data security and privacy are never compromised
                    </p>
                  </div>
                  <div className="bg-white/10 rounded-lg p-6">
                    <Target className="h-8 w-8 text-purple-400 mx-auto mb-3" />
                    <h4 className="text-white font-semibold mb-2">Innovation</h4>
                    <p className="text-gray-400 text-sm">
                      Continuously improving through cutting-edge AI technology
                    </p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link
                    to="/register"
                    className="inline-flex items-center justify-center px-6 py-3 text-lg font-medium text-white bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-200"
                  >
                    <Bot className="h-5 w-5 mr-2" />
                    Try Docu Chatbot
                  </Link>
                  <Link
                    to="/features"
                    className="inline-flex items-center justify-center px-6 py-3 text-lg font-medium text-pink-400 border-2 border-pink-400 rounded-lg hover:bg-pink-400 hover:text-white transition-all duration-200"
                  >
                    Explore Features
                  </Link>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="relative z-10 py-16 px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.6 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center"
            >
              <div className="bg-white/5 rounded-lg p-6">
                <div className="text-3xl font-bold text-pink-400 mb-2">AI-Powered</div>
                <p className="text-gray-300">Advanced language models for accurate document understanding</p>
              </div>
              <div className="bg-white/5 rounded-lg p-6">
                <div className="text-3xl font-bold text-blue-400 mb-2">Real-Time</div>
                <p className="text-gray-300">Instant responses to your document-related questions</p>
              </div>
              <div className="bg-white/5 rounded-lg p-6">
                <div className="text-3xl font-bold text-green-400 mb-2">Secure</div>
                <p className="text-gray-300">Enterprise-grade security with no permanent storage</p>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(10)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 rounded-full bg-pink-400/20"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.2, 0.6, 0.2],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default AboutUsPage;
