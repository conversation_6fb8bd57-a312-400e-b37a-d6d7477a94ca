import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Key, Save, CheckCircle, AlertCircle, ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';
import Navbar from '../components/layout/Navbar';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import { ConfigService } from '../services/configService';

const settingsSchema = z.object({
  openaiApiKey: z.string().min(1, 'OpenAI API key is required').regex(/^sk-/, 'Invalid OpenAI API key format'),
});

type SettingsFormValues = z.infer<typeof settingsSchema>;

const ApiSettingsPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [isConfigured, setIsConfigured] = useState(false);

  const { register, handleSubmit, formState: { errors }, setValue, watch } = useForm<SettingsFormValues>({
    resolver: zodResolver(settingsSchema),
  });

  const apiKey = watch('openaiApiKey');

  useEffect(() => {
    // Load existing configuration
    const config = ConfigService.getConfig();
    if (config?.openaiApiKey) {
      setValue('openaiApiKey', config.openaiApiKey);
      setIsConfigured(true);
    }
  }, [setValue]);

  const onSubmit = async (data: SettingsFormValues) => {
    setIsLoading(true);
    setSaveStatus('idle');

    try {
      ConfigService.saveConfig(data);
      setSaveStatus('success');
      setIsConfigured(true);
      
      // Clear success message after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (error) {
      console.error('Failed to save settings:', error);
      setSaveStatus('error');
      
      // Clear error message after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearConfig = () => {
    if (confirm('Are you sure you want to clear your API key configuration?')) {
      ConfigService.clearConfig();
      setValue('openaiApiKey', '');
      setIsConfigured(false);
      setSaveStatus('idle');
    }
  };

  return (
    <div className="min-h-screen bg-navy-900 flex flex-col">
      <Navbar />
      
      <motion.div
        className="flex-1 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="bg-navy-800 bg-opacity-50 backdrop-blur-sm rounded-xl p-6 md:p-8">
          {/* Header */}
          <div className="mb-8">
            <Link
              to="/settings"
              className="inline-flex items-center text-indigo-400 hover:text-indigo-300 transition-colors duration-200 mb-4"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Settings
            </Link>
            <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">API Configuration</h1>
            <p className="text-gray-300">
              Configure your OpenAI API key to enable AI-powered responses and document processing.
            </p>
          </div>
          
          <div className="space-y-8">
            {/* API Configuration Section */}
            <div className="border-b border-gray-700 pb-8">
              <div className="flex items-center mb-4">
                <Key className="h-6 w-6 text-indigo-400 mr-3" />
                <h2 className="text-xl font-semibold text-white">OpenAI API Key</h2>
              </div>
              
              <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 mb-6">
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 text-blue-400 mr-3 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-blue-200">
                    <p className="font-medium mb-1">Privacy Notice</p>
                    <p>Your API key is stored locally in your browser and never sent to our servers. It's only used to communicate directly with OpenAI's services.</p>
                  </div>
                </div>
              </div>

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div>
                  <label htmlFor="openaiApiKey" className="block text-sm font-medium text-gray-200 mb-2">
                    OpenAI API Key
                  </label>
                  <Input
                    id="openaiApiKey"
                    type="password"
                    placeholder="sk-..."
                    {...register('openaiApiKey')}
                    error={errors.openaiApiKey?.message}
                    className="bg-navy-700 text-white border-navy-600"
                  />
                  <p className="mt-2 text-sm text-gray-400">
                    Get your API key from{' '}
                    <a 
                      href="https://platform.openai.com/api-keys" 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-indigo-400 hover:text-indigo-300 underline"
                    >
                      OpenAI Platform
                    </a>
                  </p>
                </div>

                <div className="flex items-center space-x-4">
                  <Button
                    type="submit"
                    variant="primary"
                    isLoading={isLoading}
                    className="bg-indigo-600 hover:bg-indigo-700"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Configuration
                  </Button>

                  {isConfigured && (
                    <Button
                      type="button"
                      variant="secondary"
                      onClick={handleClearConfig}
                      className="bg-red-600 hover:bg-red-700 text-white"
                    >
                      Clear Configuration
                    </Button>
                  )}
                </div>

                {/* Status Messages */}
                {saveStatus === 'success' && (
                  <div className="flex items-center text-green-400">
                    <CheckCircle className="h-5 w-5 mr-2" />
                    Configuration saved successfully!
                  </div>
                )}

                {saveStatus === 'error' && (
                  <div className="flex items-center text-red-400">
                    <AlertCircle className="h-5 w-5 mr-2" />
                    Failed to save configuration. Please try again.
                  </div>
                )}

                {/* Configuration Status */}
                <div className="bg-navy-700 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-gray-200 mb-2">Configuration Status</h3>
                  <div className="flex items-center">
                    {isConfigured ? (
                      <>
                        <CheckCircle className="h-5 w-5 text-green-400 mr-2" />
                        <span className="text-green-400">API key configured</span>
                      </>
                    ) : (
                      <>
                        <AlertCircle className="h-5 w-5 text-yellow-400 mr-2" />
                        <span className="text-yellow-400">API key not configured</span>
                      </>
                    )}
                  </div>
                  {isConfigured && (
                    <p className="text-sm text-gray-400 mt-2">
                      Your bots can now use AI-powered responses and document processing.
                    </p>
                  )}
                </div>
              </form>
            </div>

            {/* Features Section */}
            <div>
              <h2 className="text-xl font-semibold text-white mb-4">What you can do with API configuration:</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-navy-700 rounded-lg p-4">
                  <h3 className="text-white font-medium mb-2">🤖 AI-Powered Responses</h3>
                  <p className="text-gray-400 text-sm">Get intelligent responses from your chatbots using GPT-4o-mini.</p>
                </div>
                <div className="bg-navy-700 rounded-lg p-4">
                  <h3 className="text-white font-medium mb-2">📄 Document Processing</h3>
                  <p className="text-gray-400 text-sm">Upload and process PDF, Word, and text documents for context-aware responses.</p>
                </div>
                <div className="bg-navy-700 rounded-lg p-4">
                  <h3 className="text-white font-medium mb-2">🔍 Smart Search</h3>
                  <p className="text-gray-400 text-sm">Find relevant information from your documents using vector similarity search.</p>
                </div>
                <div className="bg-navy-700 rounded-lg p-4">
                  <h3 className="text-white font-medium mb-2">💬 Context-Aware Chat</h3>
                  <p className="text-gray-400 text-sm">Maintain conversation context and provide personalized responses.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ApiSettingsPage;
