import { ChatOpenAI } from '@langchain/openai';
import { FaissStore } from '@langchain/community/vectorstores/faiss';
import { PromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { Chatbot } from '../types';
import { ConfigService } from './configService';

export interface ChatbotServiceConfig {
  openaiApiKey?: string;
  model?: string;
  temperature?: number;
}

export interface ChatContext {
  botName: string;
  botDescription: string;
  retrievedDocs: string;
  chatHistory: string;
}

export class ChatbotService {
  private llm: ChatOpenAI;
  private promptTemplate: PromptTemplate;

  constructor(config: ChatbotServiceConfig = {}) {
    const apiKey = config.openaiApiKey || ConfigService.getOpenAIApiKey();

    if (!apiKey) {
      throw new Error('OpenAI API key is required. Please configure it in settings.');
    }

    this.llm = new ChatOpenAI({
      openAIApiKey: apiKey,
      modelName: config.model || 'gpt-4o-mini',
      temperature: config.temperature || 0.7,
    });

    this.promptTemplate = PromptTemplate.fromTemplate(`
You are {botName}, a helpful AI assistant. Here's your description: {botDescription}

Based on the following context from uploaded documents, please answer the user's question. 
If the answer cannot be found in the provided context, politely say so and offer to help with other questions.

Context from documents:
{retrievedDocs}

Previous conversation:
{chatHistory}

Current question: {question}

Please provide a helpful and accurate response based on the context provided.
`);
  }

  async generateResponse(
    question: string,
    bot: Chatbot,
    vectorStore: FaissStore | null,
    chatHistory: string = ''
  ): Promise<string> {
    try {
      let retrievedDocs = '';

      if (vectorStore) {
        // Retrieve relevant documents
        const relevantDocs = await vectorStore.similaritySearch(question, 4);
        retrievedDocs = relevantDocs
          .map(doc => `${doc.pageContent}\n---`)
          .join('\n');
      }

      // Create the chain
      const chain = RunnableSequence.from([
        this.promptTemplate,
        this.llm,
        new StringOutputParser(),
      ]);

      // Generate response
      const response = await chain.invoke({
        botName: bot.title,
        botDescription: bot.description,
        retrievedDocs: retrievedDocs || 'No relevant documents found.',
        chatHistory: chatHistory || 'No previous conversation.',
        question: question,
      });

      return response;
    } catch (error) {
      console.error('Error generating response:', error);
      throw new Error('Failed to generate response');
    }
  }

  async generateResponseWithoutContext(
    question: string,
    bot: Chatbot,
    chatHistory: string = ''
  ): Promise<string> {
    try {
      const simplePrompt = PromptTemplate.fromTemplate(`
You are {botName}, a helpful AI assistant. Here's your description: {botDescription}

Previous conversation:
{chatHistory}

Current question: {question}

Please provide a helpful response. Note: No specific documents were uploaded for this bot, so provide general assistance based on your knowledge.
`);

      const chain = RunnableSequence.from([
        simplePrompt,
        this.llm,
        new StringOutputParser(),
      ]);

      const response = await chain.invoke({
        botName: bot.title,
        botDescription: bot.description,
        chatHistory: chatHistory || 'No previous conversation.',
        question: question,
      });

      return response;
    } catch (error) {
      console.error('Error generating response without context:', error);
      throw new Error('Failed to generate response');
    }
  }
}

export default ChatbotService;
