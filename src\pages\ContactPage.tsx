import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Phone, 
  Mail, 
  Clock, 
  Globe, 
  Twitter, 
  Linkedin, 
  Facebook,
  MessageSquare,
  ArrowLeft,
  ExternalLink,
  Send
} from 'lucide-react';
import { motion } from 'framer-motion';
import Navbar from '../components/layout/Navbar';

const ContactPage: React.FC = () => {
  const contactMethods = [
    {
      icon: Mail,
      title: "📬 Email Support",
      description: "For general inquiries, technical support, or partnership opportunities:",
      details: "✉️ (Currently not available)",
      color: "text-blue-400",
      available: false
    },
    {
      icon: Clock,
      title: "🕒 Support Hours",
      description: "Monday to Friday – 9:00 AM to 6:00 PM PST",
      details: "We typically respond within 24 hours on business days.",
      color: "text-green-400",
      available: true
    },
    {
      icon: Globe,
      title: "🌐 Website",
      description: "Visit our main website for more information:",
      details: "www.docuchatbot.com (Currently not available)",
      color: "text-purple-400",
      available: false
    },
    {
      icon: MessageSquare,
      title: "📝 Feedback & Suggestions",
      description: "Help us improve! If you have any feedback or suggestions, please fill out our short form:",
      details: "🔗 [Feedback Form Link]",
      color: "text-pink-400",
      available: false
    }
  ];

  const socialMedia = [
    {
      icon: Twitter,
      name: "🐦 Twitter",
      handle: "@docuchatbot",
      status: "(Currently not available)",
      color: "text-blue-400"
    },
    {
      icon: Linkedin,
      name: "💼 LinkedIn",
      handle: "Docu Chatbot",
      status: "(Currently not available)",
      color: "text-blue-600"
    },
    {
      icon: Facebook,
      name: "📘 Facebook",
      handle: "fb.com/docuchatbot",
      status: "(Currently not available)",
      color: "text-blue-500"
    }
  ];

  return (
    <div className="min-h-screen bg-navy-900">
      <Navbar />
      
      <div className="relative overflow-hidden">
        {/* Hero Section */}
        <div className="relative z-10 pt-20 pb-16 px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <div className="flex items-center justify-center mb-6">
                <Phone className="h-16 w-16 text-pink-400 mr-4" />
                <h1 className="text-5xl md:text-6xl font-bold text-white">
                  📞 Contact Us
                </h1>
              </div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                We'd love to hear from you! Whether you have a question, need support, or want to share feedback — we're here to help.
              </p>
            </motion.div>

            {/* Back Button */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-8"
            >
              <Link
                to="/"
                className="inline-flex items-center text-pink-400 hover:text-pink-300 transition-colors duration-200"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Home
              </Link>
            </motion.div>
          </div>
        </div>

        {/* Contact Methods Section */}
        <div className="relative z-10 py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {contactMethods.map((method, index) => {
                const IconComponent = method.icon;
                return (
                  <motion.div
                    key={index}
                    className={`bg-white/10 backdrop-blur-sm rounded-xl p-8 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-300 group ${!method.available ? 'opacity-75' : ''}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                    whileHover={{ y: -5 }}
                  >
                    <div className="flex items-start mb-4">
                      <IconComponent className={`h-8 w-8 ${method.color} mr-4 mt-1 group-hover:scale-110 transition-transform duration-300`} />
                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-white group-hover:text-pink-300 transition-colors duration-300 mb-3">
                          {method.title}
                        </h3>
                        <p className="text-gray-300 leading-relaxed mb-2">
                          {method.description}
                        </p>
                        <p className={`text-sm ${method.available ? 'text-green-400' : 'text-gray-400'} font-medium`}>
                          {method.details}
                        </p>
                        {!method.available && (
                          <span className="inline-block mt-2 px-3 py-1 bg-yellow-500/20 text-yellow-400 text-xs rounded-full">
                            Coming Soon
                          </span>
                        )}
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Social Media Section */}
        <div className="relative z-10 py-16 px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                📱 Social Media
              </h2>
              <p className="text-lg text-gray-300">
                Follow us and stay updated with our latest features and news:
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {socialMedia.map((social, index) => {
                const IconComponent = social.icon;
                return (
                  <motion.div
                    key={index}
                    className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 group opacity-75"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 1.2 + index * 0.1 }}
                  >
                    <div className="text-center">
                      <IconComponent className={`h-10 w-10 ${social.color} mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`} />
                      <h3 className="text-lg font-bold text-white mb-2">
                        {social.name}
                      </h3>
                      <p className="text-gray-300 text-sm mb-2">
                        {social.handle}
                      </p>
                      <p className="text-gray-400 text-xs">
                        {social.status}
                      </p>
                      <span className="inline-block mt-3 px-3 py-1 bg-yellow-500/20 text-yellow-400 text-xs rounded-full">
                        Coming Soon
                      </span>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Quick Contact Section */}
        <div className="relative z-10 py-16 px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.6 }}
              className="bg-gradient-to-r from-pink-500/20 to-purple-600/20 backdrop-blur-sm rounded-2xl p-8 border border-pink-500/30"
            >
              <div className="text-center">
                <Send className="h-12 w-12 text-pink-400 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-white mb-4">
                  Get in Touch
                </h3>
                <p className="text-gray-300 mb-8 leading-relaxed">
                  While our contact channels are being set up, you can still reach out to us through the platform. 
                  We're working hard to provide you with multiple ways to connect with our support team.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <div className="bg-white/10 rounded-lg p-6">
                    <Clock className="h-8 w-8 text-green-400 mx-auto mb-3" />
                    <h4 className="text-white font-semibold mb-2">Quick Response</h4>
                    <p className="text-gray-400 text-sm">
                      24-hour response time during business days
                    </p>
                  </div>
                  <div className="bg-white/10 rounded-lg p-6">
                    <MessageSquare className="h-8 w-8 text-blue-400 mx-auto mb-3" />
                    <h4 className="text-white font-semibold mb-2">Multiple Channels</h4>
                    <p className="text-gray-400 text-sm">
                      Email, social media, and feedback forms coming soon
                    </p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link
                    to="/dashboard"
                    className="inline-flex items-center justify-center px-6 py-3 text-lg font-medium text-white bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-200"
                  >
                    <MessageSquare className="h-5 w-5 mr-2" />
                    Use Platform
                  </Link>
                  <Link
                    to="/about"
                    className="inline-flex items-center justify-center px-6 py-3 text-lg font-medium text-pink-400 border-2 border-pink-400 rounded-lg hover:bg-pink-400 hover:text-white transition-all duration-200"
                  >
                    Learn More About Us
                  </Link>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 rounded-full bg-pink-400/20"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.2, 0.6, 0.2],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ContactPage;
