import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { Message, ChatSession } from '../types';
import { generateId } from '../lib/utils';
import { ChatbotService } from '../services/chatbotService';
import { DocumentProcessor } from '../services/documentProcessor';
import { ConfigService } from '../services/configService';
import { useBotStore } from './botStore';

interface ChatState {
  sessions: ChatSession[];
  currentSession: ChatSession | null;
  isLoading: boolean;
  createSession: (botId: string, userId: string) => void;
  sendMessage: (content: string) => Promise<void>;
  getSession: (id: string) => ChatSession | undefined;
  getSessions: (botId: string) => ChatSession[];
  getUserSessions: (userId: string) => ChatSession[];
  clearUserData: () => void;
}

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      sessions: [],
      currentSession: null,
      isLoading: false,

      createSession: (botId, userId) => {
        const newSession: ChatSession = {
          id: generateId(),
          botId,
          userId,
          messages: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        set(state => ({
          sessions: [...state.sessions, newSession],
          currentSession: newSession,
        }));
      },

      sendMessage: async (content) => {
        const { currentSession } = get();
        if (!currentSession) return;

        // Add user message
        const userMessage: Message = {
          id: generateId(),
          content,
          sender: 'user',
          timestamp: new Date().toISOString(),
          botId: currentSession.botId,
        };

        set(state => ({
          currentSession: state.currentSession
            ? {
                ...state.currentSession,
                messages: [...state.currentSession.messages, userMessage],
                updatedAt: new Date().toISOString(),
              }
            : null,
          sessions: state.sessions.map(session =>
            session.id === currentSession?.id
              ? {
                  ...session,
                  messages: [...session.messages, userMessage],
                  updatedAt: new Date().toISOString(),
                }
              : session
          ),
          isLoading: true,
        }));

        try {
          // Get the bot from the bot store
          const bot = useBotStore.getState().getBot(currentSession.botId);
          if (!bot) {
            throw new Error('Bot not found');
          }

          let botResponse: string;

          // Check if API key is configured
          if (!ConfigService.isConfigured()) {
            botResponse = `I'm sorry, but I need an OpenAI API key to function properly. Please configure your API key in the settings to enable AI responses.`;
          } else {
            // Initialize the chatbot service
            const chatbotService = new ChatbotService();

            // Prepare chat history
            const chatHistory = currentSession.messages
              .slice(-10) // Last 10 messages for context
              .map(msg => `${msg.sender}: ${msg.content}`)
              .join('\n');

            // Check if bot has processed documents
            if (bot.isProcessed && bot.vectorStoreData) {
              // Try to recreate vector store for retrieval
              try {
                const processor = new DocumentProcessor();
                const vectorStore = await processor.deserializeVectorStore(bot.vectorStoreData);

                if (vectorStore) {
                  botResponse = await chatbotService.generateResponse(content, bot, vectorStore, chatHistory);
                } else {
                  // Fallback: generate response without vector store but mention documents
                  botResponse = await chatbotService.generateResponseWithoutContext(content, bot, chatHistory);
                  botResponse += '\n\n(Note: Document search is temporarily unavailable, but I can still help with general questions.)';
                }
              } catch (error) {
                console.error('Error using vector store:', error);
                botResponse = await chatbotService.generateResponseWithoutContext(content, bot, chatHistory);
                botResponse += '\n\n(Note: There was an issue accessing the document knowledge base, but I can still help with general questions.)';
              }
            } else {
              // No documents processed, use general AI response
              botResponse = await chatbotService.generateResponseWithoutContext(content, bot, chatHistory);

              if (bot.files.length > 0 && !bot.isProcessed) {
                botResponse += '\n\n(Note: Your documents are still being processed. Once complete, I\'ll be able to answer questions about their content.)';
              }
            }
          }

          const botMessage: Message = {
            id: generateId(),
            content: botResponse,
            sender: 'bot',
            timestamp: new Date().toISOString(),
            botId: currentSession.botId,
          };

          set(state => ({
            currentSession: state.currentSession
              ? {
                  ...state.currentSession,
                  messages: [...state.currentSession.messages, botMessage],
                  updatedAt: new Date().toISOString(),
                }
              : null,
            sessions: state.sessions.map(session =>
              session.id === currentSession?.id
                ? {
                    ...session,
                    messages: [...session.messages, botMessage],
                    updatedAt: new Date().toISOString(),
                  }
                : session
            ),
          }));
        } catch (error) {
          console.error('Failed to get bot response:', error);

          // Add error message
          const errorMessage: Message = {
            id: generateId(),
            content: 'I apologize, but I encountered an error while processing your message. Please try again or check your API key configuration.',
            sender: 'bot',
            timestamp: new Date().toISOString(),
            botId: currentSession.botId,
          };

          set(state => ({
            currentSession: state.currentSession
              ? {
                  ...state.currentSession,
                  messages: [...state.currentSession.messages, errorMessage],
                  updatedAt: new Date().toISOString(),
                }
              : null,
            sessions: state.sessions.map(session =>
              session.id === currentSession?.id
                ? {
                    ...session,
                    messages: [...session.messages, errorMessage],
                    updatedAt: new Date().toISOString(),
                  }
                : session
            ),
          }));
        } finally {
          set({ isLoading: false });
        }
      },

      getSession: (id) => {
        return get().sessions.find(session => session.id === id);
      },

      getSessions: (botId) => {
        return get().sessions.filter(session => session.botId === botId);
      },

      getUserSessions: (userId) => {
        return get().sessions.filter(session => session.userId === userId);
      },

      clearUserData: () => {
        set({ sessions: [], currentSession: null });
      },
    }),
    {
      name: 'docuchat-chats',
      storage: createJSONStorage(() => localStorage),
    }
  )
);