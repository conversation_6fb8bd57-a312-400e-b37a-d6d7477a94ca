# Document Chatbot Platform

A modern React-based platform for creating AI-powered chatbots that can answer questions based on uploaded documents using Langchain, FAISS vector database, and OpenAI's GPT-4o-mini model.

## Features

- 🤖 **AI-Powered Chatbots**: Create intelligent bots using OpenAI's GPT-4o-mini model
- 📄 **Document Processing**: Upload and process PDF, Word, and text documents
- 🔍 **Vector Search**: FAISS-powered similarity search for relevant document retrieval
- 💬 **Context-Aware Chat**: Maintain conversation context for better responses
- 🎨 **Modern UI**: Beautiful, responsive interface built with React and Tailwind CSS
- 🔐 **Secure**: API keys stored locally, never sent to our servers

## Tech Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **AI/ML**: Langchain, OpenAI GPT-4o-mini, FAISS vector database
- **State Management**: Zustand
- **Routing**: React Router
- **Forms**: React Hook Form with Zod validation
- **Animations**: Framer Motion
- **Icons**: Lucide React

## Prerequisites

- Node.js 18+ and npm
- OpenAI API key (get one from [OpenAI Platform](https://platform.openai.com/api-keys))

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd document-chatbot-platform
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` and add your OpenAI API key:
   ```
   VITE_OPENAI_API_KEY=your_openai_api_key_here
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:5173`

## Usage

### Getting Started

1. **Register/Login**: Create an account or log in to access the platform
2. **Configure API Key**: Go to Settings → API Configuration and enter your OpenAI API key
3. **Create a Bot**: Click "Create Bot" and provide a name, description, and upload documents
4. **Chat**: Once your documents are processed, start chatting with your bot!

### Supported File Types

- **PDF**: `.pdf` files
- **Word Documents**: `.doc`, `.docx` files  
- **Text Files**: `.txt` files

### Document Processing

When you upload documents:
1. Text is extracted from your files
2. Content is split into chunks for better processing
3. Vector embeddings are created using OpenAI's embedding model
4. A FAISS vector store is built for fast similarity search
5. Your bot can now answer questions based on the document content

### Chat Features

- **Context-Aware**: Bots remember conversation history
- **Document-Based**: Responses are grounded in your uploaded documents
- **Fallback Responses**: General AI responses when document context isn't available
- **Real-time**: Fast response times with streaming support

## Configuration

### API Settings

Access via Settings → API Configuration:

- **OpenAI API Key**: Required for AI responses and document processing
- **Model**: Uses GPT-4o-mini for cost-effective, high-quality responses
- **Embeddings**: Uses text-embedding-3-small for document vectorization

### Privacy & Security

- API keys are stored locally in your browser
- Documents are processed client-side when possible
- No data is sent to our servers
- All communication is directly with OpenAI's API

## Development

### Project Structure

```
src/
├── components/          # Reusable UI components
├── pages/              # Page components
├── services/           # Business logic and API services
├── store/              # Zustand state management
├── types/              # TypeScript type definitions
└── lib/                # Utility functions
```

### Key Services

- **ConfigService**: Manages API key configuration
- **DocumentProcessor**: Handles file processing and vector store creation
- **ChatbotService**: Manages AI conversations and responses

### Building for Production

```bash
npm run build
```

## Troubleshooting

### Common Issues

1. **"API key not configured"**: Go to Settings → API Configuration and enter your OpenAI API key
2. **Document processing fails**: Ensure your API key is valid and has sufficient credits
3. **Slow responses**: Check your internet connection and OpenAI API status
4. **File upload issues**: Ensure files are in supported formats (PDF, DOC, DOCX, TXT)

### Error Messages

- **"OpenAI API key is required"**: Configure your API key in settings
- **"Failed to process documents"**: Check file format and API key validity
- **"No relevant documents found"**: Try rephrasing your question or upload more relevant documents

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the [OpenAI API documentation](https://platform.openai.com/docs)
3. Open an issue in this repository

---

Built with ❤️ using React, Langchain, and OpenAI
